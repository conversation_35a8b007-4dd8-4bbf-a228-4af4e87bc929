<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Wireless
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Wireless\V1;

use Twilio\Options;
use Twilio\Values;

abstract class RatePlanOptions
{
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     * @param bool $dataEnabled Whether SIMs can use GPRS/3G/4G/LTE data connectivity.
     * @param int $dataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on the home network (T-Mobile USA). The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB and the default value is `1000`.
     * @param string $dataMetering The model used to meter data usage. Can be: `payg` and `quota-1`, `quota-10`, and `quota-50`. Learn more about the available [data metering models](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#payg-vs-quota-data-plans).
     * @param bool $messagingEnabled Whether SIMs can make, send, and receive SMS using [Commands](https://www.twilio.com/docs/iot/wireless/api/command-resource).
     * @param bool $voiceEnabled Deprecated.
     * @param bool $nationalRoamingEnabled Whether SIMs can roam on networks other than the home network (T-Mobile USA) in the United States. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming).
     * @param string[] $internationalRoaming The list of services that SIMs capable of using GPRS/3G/4G/LTE data connectivity can use outside of the United States. Can contain: `data` and `messaging`.
     * @param int $nationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on non-home networks in the United States. The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming) for more info.
     * @param int $internationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month when roaming outside the United States. Can be up to 2TB.
     * @return CreateRatePlanOptions Options builder
     */
    public static function create(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $dataEnabled = Values::BOOL_NONE,
        int $dataLimit = Values::INT_NONE,
        string $dataMetering = Values::NONE,
        bool $messagingEnabled = Values::BOOL_NONE,
        bool $voiceEnabled = Values::BOOL_NONE,
        bool $nationalRoamingEnabled = Values::BOOL_NONE,
        array $internationalRoaming = Values::ARRAY_NONE,
        int $nationalRoamingDataLimit = Values::INT_NONE,
        int $internationalRoamingDataLimit = Values::INT_NONE

    ): CreateRatePlanOptions
    {
        return new CreateRatePlanOptions(
            $uniqueName,
            $friendlyName,
            $dataEnabled,
            $dataLimit,
            $dataMetering,
            $messagingEnabled,
            $voiceEnabled,
            $nationalRoamingEnabled,
            $internationalRoaming,
            $nationalRoamingDataLimit,
            $internationalRoamingDataLimit
        );
    }




    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     * @return UpdateRatePlanOptions Options builder
     */
    public static function update(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE

    ): UpdateRatePlanOptions
    {
        return new UpdateRatePlanOptions(
            $uniqueName,
            $friendlyName
        );
    }

}

class CreateRatePlanOptions extends Options
    {
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     * @param bool $dataEnabled Whether SIMs can use GPRS/3G/4G/LTE data connectivity.
     * @param int $dataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on the home network (T-Mobile USA). The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB and the default value is `1000`.
     * @param string $dataMetering The model used to meter data usage. Can be: `payg` and `quota-1`, `quota-10`, and `quota-50`. Learn more about the available [data metering models](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#payg-vs-quota-data-plans).
     * @param bool $messagingEnabled Whether SIMs can make, send, and receive SMS using [Commands](https://www.twilio.com/docs/iot/wireless/api/command-resource).
     * @param bool $voiceEnabled Deprecated.
     * @param bool $nationalRoamingEnabled Whether SIMs can roam on networks other than the home network (T-Mobile USA) in the United States. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming).
     * @param string[] $internationalRoaming The list of services that SIMs capable of using GPRS/3G/4G/LTE data connectivity can use outside of the United States. Can contain: `data` and `messaging`.
     * @param int $nationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on non-home networks in the United States. The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming) for more info.
     * @param int $internationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month when roaming outside the United States. Can be up to 2TB.
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE,
        bool $dataEnabled = Values::BOOL_NONE,
        int $dataLimit = Values::INT_NONE,
        string $dataMetering = Values::NONE,
        bool $messagingEnabled = Values::BOOL_NONE,
        bool $voiceEnabled = Values::BOOL_NONE,
        bool $nationalRoamingEnabled = Values::BOOL_NONE,
        array $internationalRoaming = Values::ARRAY_NONE,
        int $nationalRoamingDataLimit = Values::INT_NONE,
        int $internationalRoamingDataLimit = Values::INT_NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['friendlyName'] = $friendlyName;
        $this->options['dataEnabled'] = $dataEnabled;
        $this->options['dataLimit'] = $dataLimit;
        $this->options['dataMetering'] = $dataMetering;
        $this->options['messagingEnabled'] = $messagingEnabled;
        $this->options['voiceEnabled'] = $voiceEnabled;
        $this->options['nationalRoamingEnabled'] = $nationalRoamingEnabled;
        $this->options['internationalRoaming'] = $internationalRoaming;
        $this->options['nationalRoamingDataLimit'] = $nationalRoamingDataLimit;
        $this->options['internationalRoamingDataLimit'] = $internationalRoamingDataLimit;
    }

    /**
     * An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * A descriptive string that you create to describe the resource. It does not have to be unique.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Whether SIMs can use GPRS/3G/4G/LTE data connectivity.
     *
     * @param bool $dataEnabled Whether SIMs can use GPRS/3G/4G/LTE data connectivity.
     * @return $this Fluent Builder
     */
    public function setDataEnabled(bool $dataEnabled): self
    {
        $this->options['dataEnabled'] = $dataEnabled;
        return $this;
    }

    /**
     * The total data usage (download and upload combined) in Megabytes that the Network allows during one month on the home network (T-Mobile USA). The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB and the default value is `1000`.
     *
     * @param int $dataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on the home network (T-Mobile USA). The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB and the default value is `1000`.
     * @return $this Fluent Builder
     */
    public function setDataLimit(int $dataLimit): self
    {
        $this->options['dataLimit'] = $dataLimit;
        return $this;
    }

    /**
     * The model used to meter data usage. Can be: `payg` and `quota-1`, `quota-10`, and `quota-50`. Learn more about the available [data metering models](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#payg-vs-quota-data-plans).
     *
     * @param string $dataMetering The model used to meter data usage. Can be: `payg` and `quota-1`, `quota-10`, and `quota-50`. Learn more about the available [data metering models](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#payg-vs-quota-data-plans).
     * @return $this Fluent Builder
     */
    public function setDataMetering(string $dataMetering): self
    {
        $this->options['dataMetering'] = $dataMetering;
        return $this;
    }

    /**
     * Whether SIMs can make, send, and receive SMS using [Commands](https://www.twilio.com/docs/iot/wireless/api/command-resource).
     *
     * @param bool $messagingEnabled Whether SIMs can make, send, and receive SMS using [Commands](https://www.twilio.com/docs/iot/wireless/api/command-resource).
     * @return $this Fluent Builder
     */
    public function setMessagingEnabled(bool $messagingEnabled): self
    {
        $this->options['messagingEnabled'] = $messagingEnabled;
        return $this;
    }

    /**
     * Deprecated.
     *
     * @param bool $voiceEnabled Deprecated.
     * @return $this Fluent Builder
     */
    public function setVoiceEnabled(bool $voiceEnabled): self
    {
        $this->options['voiceEnabled'] = $voiceEnabled;
        return $this;
    }

    /**
     * Whether SIMs can roam on networks other than the home network (T-Mobile USA) in the United States. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming).
     *
     * @param bool $nationalRoamingEnabled Whether SIMs can roam on networks other than the home network (T-Mobile USA) in the United States. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming).
     * @return $this Fluent Builder
     */
    public function setNationalRoamingEnabled(bool $nationalRoamingEnabled): self
    {
        $this->options['nationalRoamingEnabled'] = $nationalRoamingEnabled;
        return $this;
    }

    /**
     * The list of services that SIMs capable of using GPRS/3G/4G/LTE data connectivity can use outside of the United States. Can contain: `data` and `messaging`.
     *
     * @param string[] $internationalRoaming The list of services that SIMs capable of using GPRS/3G/4G/LTE data connectivity can use outside of the United States. Can contain: `data` and `messaging`.
     * @return $this Fluent Builder
     */
    public function setInternationalRoaming(array $internationalRoaming): self
    {
        $this->options['internationalRoaming'] = $internationalRoaming;
        return $this;
    }

    /**
     * The total data usage (download and upload combined) in Megabytes that the Network allows during one month on non-home networks in the United States. The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming) for more info.
     *
     * @param int $nationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month on non-home networks in the United States. The metering period begins the day of activation and ends on the same day in the following month. Can be up to 2TB. See [national roaming](https://www.twilio.com/docs/iot/wireless/api/rateplan-resource#national-roaming) for more info.
     * @return $this Fluent Builder
     */
    public function setNationalRoamingDataLimit(int $nationalRoamingDataLimit): self
    {
        $this->options['nationalRoamingDataLimit'] = $nationalRoamingDataLimit;
        return $this;
    }

    /**
     * The total data usage (download and upload combined) in Megabytes that the Network allows during one month when roaming outside the United States. Can be up to 2TB.
     *
     * @param int $internationalRoamingDataLimit The total data usage (download and upload combined) in Megabytes that the Network allows during one month when roaming outside the United States. Can be up to 2TB.
     * @return $this Fluent Builder
     */
    public function setInternationalRoamingDataLimit(int $internationalRoamingDataLimit): self
    {
        $this->options['internationalRoamingDataLimit'] = $internationalRoamingDataLimit;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Wireless.V1.CreateRatePlanOptions ' . $options . ']';
    }
}




class UpdateRatePlanOptions extends Options
    {
    /**
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     */
    public function __construct(
        
        string $uniqueName = Values::NONE,
        string $friendlyName = Values::NONE

    ) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['friendlyName'] = $friendlyName;
    }

    /**
     * An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     *
     * @param string $uniqueName An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self
    {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * A descriptive string that you create to describe the resource. It does not have to be unique.
     *
     * @param string $friendlyName A descriptive string that you create to describe the resource. It does not have to be unique.
     * @return $this Fluent Builder
     */
    public function setFriendlyName(string $friendlyName): self
    {
        $this->options['friendlyName'] = $friendlyName;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Wireless.V1.UpdateRatePlanOptions ' . $options . ']';
    }
}

