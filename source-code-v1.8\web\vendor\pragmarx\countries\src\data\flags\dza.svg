<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="900" height="600" viewBox="0 0 60 40">
<clipPath id="c">
<!-- Inner circle of crescent shifted right from center by 5*sqrt(3)-sqrt(39) = 2.415256 -->
<path d="M0,0H60V40H0zM32.415256,28a8,8 0 1,0 0,-16a8,8 0 1,0 0,16"/>
</clipPath>
<path d="M0,0H60V40H0z" fill="#fff"/>
<path d="M0,0H30V40H0z" fill="#006233"/>
<g fill="#d21034">
<circle cx="30" cy="20" r="10" clip-path="url(#c)"/>
<!-- Distance between center of star and center of flag is 5*cos(36 deg) = 4.045085 -->
<g transform="translate(34.045085,20)">
<g id="f">
<g id="t">
<path d="M5,0H0V3z" transform="rotate(18,5,0)" id="o"/>
<use xlink:href="#o" transform="scale(1,-1)"/>
</g>
<use xlink:href="#t" transform="rotate(72)"/>
</g>
<use xlink:href="#t" transform="rotate(-72)"/>
<use xlink:href="#f" transform="rotate(144)"/>
</g>
</g>
</svg>