<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata extends \Google\Model
{
  /**
   * @var string
   */
  public $dataState;
  /**
   * @var string
   */
  public $lastTrainTime;
  /**
   * @var string
   */
  public $lastTuneTime;
  /**
   * @var string
   */
  public $servingState;
  /**
   * @var string
   */
  public $tuningOperation;

  /**
   * @param string
   */
  public function setDataState($dataState)
  {
    $this->dataState = $dataState;
  }
  /**
   * @return string
   */
  public function getDataState()
  {
    return $this->dataState;
  }
  /**
   * @param string
   */
  public function setLastTrainTime($lastTrainTime)
  {
    $this->lastTrainTime = $lastTrainTime;
  }
  /**
   * @return string
   */
  public function getLastTrainTime()
  {
    return $this->lastTrainTime;
  }
  /**
   * @param string
   */
  public function setLastTuneTime($lastTuneTime)
  {
    $this->lastTuneTime = $lastTuneTime;
  }
  /**
   * @return string
   */
  public function getLastTuneTime()
  {
    return $this->lastTuneTime;
  }
  /**
   * @param string
   */
  public function setServingState($servingState)
  {
    $this->servingState = $servingState;
  }
  /**
   * @return string
   */
  public function getServingState()
  {
    return $this->servingState;
  }
  /**
   * @param string
   */
  public function setTuningOperation($tuningOperation)
  {
    $this->tuningOperation = $tuningOperation;
  }
  /**
   * @return string
   */
  public function getTuningOperation()
  {
    return $this->tuningOperation;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1alphaEngineRecommendationMetadata');
