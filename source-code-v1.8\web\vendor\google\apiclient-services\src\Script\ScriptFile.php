<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Script;

class ScriptFile extends \Google\Model
{
  /**
   * @var string
   */
  public $createTime;
  protected $functionSetType = GoogleAppsScriptTypeFunctionSet::class;
  protected $functionSetDataType = '';
  protected $lastModifyUserType = GoogleAppsScriptTypeUser::class;
  protected $lastModifyUserDataType = '';
  /**
   * @var string
   */
  public $name;
  /**
   * @var string
   */
  public $source;
  /**
   * @var string
   */
  public $type;
  /**
   * @var string
   */
  public $updateTime;

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param GoogleAppsScriptTypeFunctionSet
   */
  public function setFunctionSet(GoogleAppsScriptTypeFunctionSet $functionSet)
  {
    $this->functionSet = $functionSet;
  }
  /**
   * @return GoogleAppsScriptTypeFunctionSet
   */
  public function getFunctionSet()
  {
    return $this->functionSet;
  }
  /**
   * @param GoogleAppsScriptTypeUser
   */
  public function setLastModifyUser(GoogleAppsScriptTypeUser $lastModifyUser)
  {
    $this->lastModifyUser = $lastModifyUser;
  }
  /**
   * @return GoogleAppsScriptTypeUser
   */
  public function getLastModifyUser()
  {
    return $this->lastModifyUser;
  }
  /**
   * @param string
   */
  public function setName($name)
  {
    $this->name = $name;
  }
  /**
   * @return string
   */
  public function getName()
  {
    return $this->name;
  }
  /**
   * @param string
   */
  public function setSource($source)
  {
    $this->source = $source;
  }
  /**
   * @return string
   */
  public function getSource()
  {
    return $this->source;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
  /**
   * @param string
   */
  public function setUpdateTime($updateTime)
  {
    $this->updateTime = $updateTime;
  }
  /**
   * @return string
   */
  public function getUpdateTime()
  {
    return $this->updateTime;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ScriptFile::class, 'Google_Service_Script_ScriptFile');
