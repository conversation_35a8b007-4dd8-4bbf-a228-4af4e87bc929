<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Video\V1\Room\Participant;

use Twilio\ListResource;
use Twilio\Version;


class AnonymizeList extends ListResource
    {
    /**
     * Construct the AnonymizeList
     *
     * @param Version $version Version that contains the resource
     * @param string $roomSid The SID of the room with the participant to update.
     * @param string $sid The SID of the RoomParticipant resource to update.
     */
    public function __construct(
        Version $version,
        string $roomSid,
        string $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'roomSid' =>
            $roomSid,
        
        'sid' =>
            $sid,
        
        ];
    }

    /**
     * Constructs a AnonymizeContext
     */
    public function getContext(
        
    ): AnonymizeContext
    {
        return new AnonymizeContext(
            $this->version,
            $this->solution['roomSid'],
            $this->solution['sid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Video.V1.AnonymizeList]';
    }
}
