<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Compute;

class ServiceAttachmentConnectedEndpoint extends \Google\Model
{
  /**
   * @var string
   */
  public $consumerNetwork;
  /**
   * @var string
   */
  public $endpoint;
  /**
   * @var string
   */
  public $propagatedConnectionCount;
  /**
   * @var string
   */
  public $pscConnectionId;
  /**
   * @var string
   */
  public $status;

  /**
   * @param string
   */
  public function setConsumerNetwork($consumerNetwork)
  {
    $this->consumerNetwork = $consumerNetwork;
  }
  /**
   * @return string
   */
  public function getConsumerNetwork()
  {
    return $this->consumerNetwork;
  }
  /**
   * @param string
   */
  public function setEndpoint($endpoint)
  {
    $this->endpoint = $endpoint;
  }
  /**
   * @return string
   */
  public function getEndpoint()
  {
    return $this->endpoint;
  }
  /**
   * @param string
   */
  public function setPropagatedConnectionCount($propagatedConnectionCount)
  {
    $this->propagatedConnectionCount = $propagatedConnectionCount;
  }
  /**
   * @return string
   */
  public function getPropagatedConnectionCount()
  {
    return $this->propagatedConnectionCount;
  }
  /**
   * @param string
   */
  public function setPscConnectionId($pscConnectionId)
  {
    $this->pscConnectionId = $pscConnectionId;
  }
  /**
   * @return string
   */
  public function getPscConnectionId()
  {
    return $this->pscConnectionId;
  }
  /**
   * @param string
   */
  public function setStatus($status)
  {
    $this->status = $status;
  }
  /**
   * @return string
   */
  public function getStatus()
  {
    return $this->status;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ServiceAttachmentConnectedEndpoint::class, 'Google_Service_Compute_ServiceAttachmentConnectedEndpoint');
