<?php declare(strict_types=1);
/*
 * This file is part of sebastian/code-unit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace Sebastian<PERSON><PERSON>\CodeUnit;

/**
 * @psalm-immutable
 */
final class ClassUnit extends CodeUnit
{
    /**
     * @psalm-assert-if-true ClassUnit $this
     */
    public function isClass(): bool
    {
        return true;
    }
}
