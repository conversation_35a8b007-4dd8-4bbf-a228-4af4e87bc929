<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Taskrouter
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Taskrouter\V1\Workspace;

use Twilio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Values;
use Twilio\Version;
use Twilio\Deserialize;


/**
 * @property string|null $accountSid
 * @property string|null $actorSid
 * @property string|null $actorType
 * @property string|null $actorUrl
 * @property string|null $description
 * @property array|null $eventData
 * @property \DateTime|null $eventDate
 * @property int|null $eventDateMs
 * @property string|null $eventType
 * @property string|null $resourceSid
 * @property string|null $resourceType
 * @property string|null $resourceUrl
 * @property string|null $sid
 * @property string|null $source
 * @property string|null $sourceIpAddress
 * @property string|null $url
 * @property string|null $workspaceSid
 */
class EventInstance extends InstanceResource
{
    /**
     * Initialize the EventInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $workspaceSid The SID of the Workspace with the Event to fetch.
     * @param string $sid The SID of the Event resource to fetch.
     */
    public function __construct(Version $version, array $payload, string $workspaceSid, string $sid = null)
    {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'actorSid' => Values::array_get($payload, 'actor_sid'),
            'actorType' => Values::array_get($payload, 'actor_type'),
            'actorUrl' => Values::array_get($payload, 'actor_url'),
            'description' => Values::array_get($payload, 'description'),
            'eventData' => Values::array_get($payload, 'event_data'),
            'eventDate' => Deserialize::dateTime(Values::array_get($payload, 'event_date')),
            'eventDateMs' => Values::array_get($payload, 'event_date_ms'),
            'eventType' => Values::array_get($payload, 'event_type'),
            'resourceSid' => Values::array_get($payload, 'resource_sid'),
            'resourceType' => Values::array_get($payload, 'resource_type'),
            'resourceUrl' => Values::array_get($payload, 'resource_url'),
            'sid' => Values::array_get($payload, 'sid'),
            'source' => Values::array_get($payload, 'source'),
            'sourceIpAddress' => Values::array_get($payload, 'source_ip_address'),
            'url' => Values::array_get($payload, 'url'),
            'workspaceSid' => Values::array_get($payload, 'workspace_sid'),
        ];

        $this->solution = ['workspaceSid' => $workspaceSid, 'sid' => $sid ?: $this->properties['sid'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return EventContext Context for this EventInstance
     */
    protected function proxy(): EventContext
    {
        if (!$this->context) {
            $this->context = new EventContext(
                $this->version,
                $this->solution['workspaceSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the EventInstance
     *
     * @return EventInstance Fetched EventInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): EventInstance
    {

        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name)
    {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Taskrouter.V1.EventInstance ' . \implode(' ', $context) . ']';
    }
}

