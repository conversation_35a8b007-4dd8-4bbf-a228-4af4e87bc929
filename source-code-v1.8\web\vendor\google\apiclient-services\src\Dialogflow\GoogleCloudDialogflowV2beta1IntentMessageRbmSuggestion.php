<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dialogflow;

class GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestion extends \Google\Model
{
  protected $actionType = GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedAction::class;
  protected $actionDataType = '';
  protected $replyType = GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedReply::class;
  protected $replyDataType = '';

  /**
   * @param GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedAction
   */
  public function setAction(GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedAction $action)
  {
    $this->action = $action;
  }
  /**
   * @return GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedAction
   */
  public function getAction()
  {
    return $this->action;
  }
  /**
   * @param GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedReply
   */
  public function setReply(GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedReply $reply)
  {
    $this->reply = $reply;
  }
  /**
   * @return GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestedReply
   */
  public function getReply()
  {
    return $this->reply;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestion::class, 'Google_Service_Dialogflow_GoogleCloudDialogflowV2beta1IntentMessageRbmSuggestion');
