<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dialogflow;

class GoogleCloudDialogflowCxV3beta1ImportIntentsResponse extends \Google\Collection
{
  protected $collection_key = 'intents';
  protected $conflictingResourcesType = GoogleCloudDialogflowCxV3beta1ImportIntentsResponseConflictingResources::class;
  protected $conflictingResourcesDataType = '';
  /**
   * @var string[]
   */
  public $intents;

  /**
   * @param GoogleCloudDialogflowCxV3beta1ImportIntentsResponseConflictingResources
   */
  public function setConflictingResources(GoogleCloudDialogflowCxV3beta1ImportIntentsResponseConflictingResources $conflictingResources)
  {
    $this->conflictingResources = $conflictingResources;
  }
  /**
   * @return GoogleCloudDialogflowCxV3beta1ImportIntentsResponseConflictingResources
   */
  public function getConflictingResources()
  {
    return $this->conflictingResources;
  }
  /**
   * @param string[]
   */
  public function setIntents($intents)
  {
    $this->intents = $intents;
  }
  /**
   * @return string[]
   */
  public function getIntents()
  {
    return $this->intents;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDialogflowCxV3beta1ImportIntentsResponse::class, 'Google_Service_Dialogflow_GoogleCloudDialogflowCxV3beta1ImportIntentsResponse');
