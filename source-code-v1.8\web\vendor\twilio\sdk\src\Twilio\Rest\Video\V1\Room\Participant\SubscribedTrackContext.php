<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Video
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


namespace Twilio\Rest\Video\V1\Room\Participant;

use Twilio\Exceptions\TwilioException;
use Twilio\Version;
use Twilio\InstanceContext;


class SubscribedTrackContext extends InstanceContext
    {
    /**
     * Initialize the SubscribedTrackContext
     *
     * @param Version $version Version that contains the resource
     * @param string $roomSid The SID of the Room where the Track resource to fetch is subscribed.
     * @param string $participantSid The SID of the participant that subscribes to the Track resource to fetch.
     * @param string $sid The SID of the RoomParticipantSubscribedTrack resource to fetch.
     */
    public function __construct(
        Version $version,
        $roomSid,
        $participantSid,
        $sid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'roomSid' =>
            $roomSid,
        'participantSid' =>
            $participantSid,
        'sid' =>
            $sid,
        ];

        $this->uri = '/Rooms/' . \rawurlencode($roomSid)
        .'/Participants/' . \rawurlencode($participantSid)
        .'/SubscribedTracks/' . \rawurlencode($sid)
        .'';
    }

    /**
     * Fetch the SubscribedTrackInstance
     *
     * @return SubscribedTrackInstance Fetched SubscribedTrackInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): SubscribedTrackInstance
    {

        $payload = $this->version->fetch('GET', $this->uri, [], []);

        return new SubscribedTrackInstance(
            $this->version,
            $payload,
            $this->solution['roomSid'],
            $this->solution['participantSid'],
            $this->solution['sid']
        );
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Video.V1.SubscribedTrackContext ' . \implode(' ', $context) . ']';
    }
}
