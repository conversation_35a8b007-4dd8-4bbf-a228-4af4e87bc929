<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\Dialogflow;

class GoogleCloudDialogflowV2CreateConversationModelEvaluationOperationMetadata extends \Google\Model
{
  /**
   * @var string
   */
  public $conversationModel;
  /**
   * @var string
   */
  public $conversationModelEvaluation;
  /**
   * @var string
   */
  public $createTime;
  /**
   * @var string
   */
  public $state;

  /**
   * @param string
   */
  public function setConversationModel($conversationModel)
  {
    $this->conversationModel = $conversationModel;
  }
  /**
   * @return string
   */
  public function getConversationModel()
  {
    return $this->conversationModel;
  }
  /**
   * @param string
   */
  public function setConversationModelEvaluation($conversationModelEvaluation)
  {
    $this->conversationModelEvaluation = $conversationModelEvaluation;
  }
  /**
   * @return string
   */
  public function getConversationModelEvaluation()
  {
    return $this->conversationModelEvaluation;
  }
  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param string
   */
  public function setState($state)
  {
    $this->state = $state;
  }
  /**
   * @return string
   */
  public function getState()
  {
    return $this->state;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDialogflowV2CreateConversationModelEvaluationOperationMetadata::class, 'Google_Service_Dialogflow_GoogleCloudDialogflowV2CreateConversationModelEvaluationOperationMetadata');
