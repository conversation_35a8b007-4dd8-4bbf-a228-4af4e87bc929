<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponse extends \Google\Collection
{
  protected $collection_key = 'failureSamples';
  /**
   * @var string[]
   */
  public $failedUris;
  protected $failureSamplesType = GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo::class;
  protected $failureSamplesDataType = 'array';

  /**
   * @param string[]
   */
  public function setFailedUris($failedUris)
  {
    $this->failedUris = $failedUris;
  }
  /**
   * @return string[]
   */
  public function getFailedUris()
  {
    return $this->failedUris;
  }
  /**
   * @param GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo[]
   */
  public function setFailureSamples($failureSamples)
  {
    $this->failureSamples = $failureSamples;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponseFailureInfo[]
   */
  public function getFailureSamples()
  {
    return $this->failureSamples;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponse::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1alphaRecrawlUrisResponse');
