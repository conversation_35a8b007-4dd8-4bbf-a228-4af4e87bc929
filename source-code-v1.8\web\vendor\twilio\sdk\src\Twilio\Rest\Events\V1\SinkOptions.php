<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Events
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Events\V1;

use Twilio\Options;
use Twilio\Values;

abstract class SinkOptions
{



    /**
     * @param bool $inUse A boolean query parameter filtering the results to return sinks used/not used by a subscription.
     * @param string $status A String query parameter filtering the results by status `initialized`, `validating`, `active` or `failed`.
     * @return ReadSinkOptions Options builder
     */
    public static function read(
        
        bool $inUse = Values::BOOL_NONE,
        string $status = Values::NONE

    ): ReadSinkOptions
    {
        return new ReadSinkOptions(
            $inUse,
            $status
        );
    }


}




class ReadSinkOptions extends Options
    {
    /**
     * @param bool $inUse A boolean query parameter filtering the results to return sinks used/not used by a subscription.
     * @param string $status A String query parameter filtering the results by status `initialized`, `validating`, `active` or `failed`.
     */
    public function __construct(
        
        bool $inUse = Values::BOOL_NONE,
        string $status = Values::NONE

    ) {
        $this->options['inUse'] = $inUse;
        $this->options['status'] = $status;
    }

    /**
     * A boolean query parameter filtering the results to return sinks used/not used by a subscription.
     *
     * @param bool $inUse A boolean query parameter filtering the results to return sinks used/not used by a subscription.
     * @return $this Fluent Builder
     */
    public function setInUse(bool $inUse): self
    {
        $this->options['inUse'] = $inUse;
        return $this;
    }

    /**
     * A String query parameter filtering the results by status `initialized`, `validating`, `active` or `failed`.
     *
     * @param string $status A String query parameter filtering the results by status `initialized`, `validating`, `active` or `failed`.
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self
    {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Events.V1.ReadSinkOptions ' . $options . ']';
    }
}


