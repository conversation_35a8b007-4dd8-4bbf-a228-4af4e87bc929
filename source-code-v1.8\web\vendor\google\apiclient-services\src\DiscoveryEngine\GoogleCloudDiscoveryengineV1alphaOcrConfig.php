<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1alphaOcrConfig extends \Google\Collection
{
  protected $collection_key = 'enhancedDocumentElements';
  /**
   * @var bool
   */
  public $enabled;
  /**
   * @var string[]
   */
  public $enhancedDocumentElements;
  /**
   * @var bool
   */
  public $useNativeText;

  /**
   * @param bool
   */
  public function setEnabled($enabled)
  {
    $this->enabled = $enabled;
  }
  /**
   * @return bool
   */
  public function getEnabled()
  {
    return $this->enabled;
  }
  /**
   * @param string[]
   */
  public function setEnhancedDocumentElements($enhancedDocumentElements)
  {
    $this->enhancedDocumentElements = $enhancedDocumentElements;
  }
  /**
   * @return string[]
   */
  public function getEnhancedDocumentElements()
  {
    return $this->enhancedDocumentElements;
  }
  /**
   * @param bool
   */
  public function setUseNativeText($useNativeText)
  {
    $this->useNativeText = $useNativeText;
  }
  /**
   * @return bool
   */
  public function getUseNativeText()
  {
    return $this->useNativeText;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1alphaOcrConfig::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1alphaOcrConfig');
