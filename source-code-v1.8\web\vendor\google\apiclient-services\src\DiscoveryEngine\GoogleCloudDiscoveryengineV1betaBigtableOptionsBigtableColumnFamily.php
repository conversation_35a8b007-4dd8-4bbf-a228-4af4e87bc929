<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumnFamily extends \Google\Collection
{
  protected $collection_key = 'columns';
  protected $columnsType = GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumn::class;
  protected $columnsDataType = 'array';
  /**
   * @var string
   */
  public $encoding;
  /**
   * @var string
   */
  public $fieldName;
  /**
   * @var string
   */
  public $type;

  /**
   * @param GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumn[]
   */
  public function setColumns($columns)
  {
    $this->columns = $columns;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumn[]
   */
  public function getColumns()
  {
    return $this->columns;
  }
  /**
   * @param string
   */
  public function setEncoding($encoding)
  {
    $this->encoding = $encoding;
  }
  /**
   * @return string
   */
  public function getEncoding()
  {
    return $this->encoding;
  }
  /**
   * @param string
   */
  public function setFieldName($fieldName)
  {
    $this->fieldName = $fieldName;
  }
  /**
   * @return string
   */
  public function getFieldName()
  {
    return $this->fieldName;
  }
  /**
   * @param string
   */
  public function setType($type)
  {
    $this->type = $type;
  }
  /**
   * @return string
   */
  public function getType()
  {
    return $this->type;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumnFamily::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1betaBigtableOptionsBigtableColumnFamily');
