<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleApiDistributionBucketOptionsLinear extends \Google\Model
{
  /**
   * @var int
   */
  public $numFiniteBuckets;
  public $offset;
  public $width;

  /**
   * @param int
   */
  public function setNumFiniteBuckets($numFiniteBuckets)
  {
    $this->numFiniteBuckets = $numFiniteBuckets;
  }
  /**
   * @return int
   */
  public function getNumFiniteBuckets()
  {
    return $this->numFiniteBuckets;
  }
  public function setOffset($offset)
  {
    $this->offset = $offset;
  }
  public function getOffset()
  {
    return $this->offset;
  }
  public function setWidth($width)
  {
    $this->width = $width;
  }
  public function getWidth()
  {
    return $this->width;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleApiDistributionBucketOptionsLinear::class, 'Google_Service_DiscoveryEngine_GoogleApiDistributionBucketOptionsLinear');
