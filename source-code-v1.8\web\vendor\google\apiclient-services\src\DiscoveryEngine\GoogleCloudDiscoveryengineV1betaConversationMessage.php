<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1betaConversationMessage extends \Google\Model
{
  /**
   * @var string
   */
  public $createTime;
  protected $replyType = GoogleCloudDiscoveryengineV1betaReply::class;
  protected $replyDataType = '';
  protected $userInputType = GoogleCloudDiscoveryengineV1betaTextInput::class;
  protected $userInputDataType = '';

  /**
   * @param string
   */
  public function setCreateTime($createTime)
  {
    $this->createTime = $createTime;
  }
  /**
   * @return string
   */
  public function getCreateTime()
  {
    return $this->createTime;
  }
  /**
   * @param GoogleCloudDiscoveryengineV1betaReply
   */
  public function setReply(GoogleCloudDiscoveryengineV1betaReply $reply)
  {
    $this->reply = $reply;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1betaReply
   */
  public function getReply()
  {
    return $this->reply;
  }
  /**
   * @param GoogleCloudDiscoveryengineV1betaTextInput
   */
  public function setUserInput(GoogleCloudDiscoveryengineV1betaTextInput $userInput)
  {
    $this->userInput = $userInput;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1betaTextInput
   */
  public function getUserInput()
  {
    return $this->userInput;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1betaConversationMessage::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1betaConversationMessage');
