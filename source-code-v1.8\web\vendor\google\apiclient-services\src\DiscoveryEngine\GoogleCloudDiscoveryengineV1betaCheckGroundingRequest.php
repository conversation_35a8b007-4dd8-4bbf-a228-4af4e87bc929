<?php
/*
 * Copyright 2014 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */

namespace Google\Service\DiscoveryEngine;

class GoogleCloudDiscoveryengineV1betaCheckGroundingRequest extends \Google\Collection
{
  protected $collection_key = 'facts';
  /**
   * @var string
   */
  public $answerCandidate;
  protected $factsType = GoogleCloudDiscoveryengineV1betaGroundingFact::class;
  protected $factsDataType = 'array';
  protected $groundingSpecType = GoogleCloudDiscoveryengineV1betaCheckGroundingSpec::class;
  protected $groundingSpecDataType = '';

  /**
   * @param string
   */
  public function setAnswerCandidate($answerCandidate)
  {
    $this->answerCandidate = $answerCandidate;
  }
  /**
   * @return string
   */
  public function getAnswerCandidate()
  {
    return $this->answerCandidate;
  }
  /**
   * @param GoogleCloudDiscoveryengineV1betaGroundingFact[]
   */
  public function setFacts($facts)
  {
    $this->facts = $facts;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1betaGroundingFact[]
   */
  public function getFacts()
  {
    return $this->facts;
  }
  /**
   * @param GoogleCloudDiscoveryengineV1betaCheckGroundingSpec
   */
  public function setGroundingSpec(GoogleCloudDiscoveryengineV1betaCheckGroundingSpec $groundingSpec)
  {
    $this->groundingSpec = $groundingSpec;
  }
  /**
   * @return GoogleCloudDiscoveryengineV1betaCheckGroundingSpec
   */
  public function getGroundingSpec()
  {
    return $this->groundingSpec;
  }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(GoogleCloudDiscoveryengineV1betaCheckGroundingRequest::class, 'Google_Service_DiscoveryEngine_GoogleCloudDiscoveryengineV1betaCheckGroundingRequest');
