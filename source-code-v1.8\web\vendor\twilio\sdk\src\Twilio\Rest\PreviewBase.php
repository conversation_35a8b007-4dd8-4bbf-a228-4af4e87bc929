<?php
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Preview\DeployedDevices;
use Twilio\Rest\Preview\HostedNumbers;
use Twilio\Rest\Preview\Sync;
use Twilio\Rest\Preview\Marketplace;
use Twilio\Rest\Preview\Wireless;

/**
 * @property \Twilio\Rest\Preview\DeployedDevices $deployedDevices
 * @property \Twilio\Rest\Preview\HostedNumbers $hostedNumbers
 * @property \Twilio\Rest\Preview\Sync $sync
 * @property \Twilio\Rest\Preview\Marketplace $marketplace
 * @property \Twilio\Rest\Preview\Wireless $wireless
 */
class PreviewBase extends Domain {
    protected $_deployedDevices;
    protected $_hostedNumbers;
    protected $_sync;
    protected $_marketplace;
    protected $_wireless;

    /**
     * Construct the Preview Domain
     *
     * @param Client $client Client to communicate with Twilio
     */
    public function __construct(Client $client) {
        parent::__construct($client);

        $this->baseUrl = 'https://preview.twilio.com';
    }


    /**
     * @return DeployedDevices Version deployedDevices of preview
     */
    protected function getDeployedDevices(): DeployedDevices {
        if (!$this->_deployedDevices) {
            $this->_deployedDevices = new DeployedDevices($this);
        }
        return $this->_deployedDevices;
    }

    /**
     * @return HostedNumbers Version hostedNumbers of preview
     */
    protected function getHostedNumbers(): HostedNumbers {
        if (!$this->_hostedNumbers) {
            $this->_hostedNumbers = new HostedNumbers($this);
        }
        return $this->_hostedNumbers;
    }

    /**
     * @return Sync Version sync of preview
     */
    protected function getSync(): Sync {
        if (!$this->_sync) {
            $this->_sync = new Sync($this);
        }
        return $this->_sync;
    }

    /**
     * @return Marketplace Version marketplace of preview
     */
    protected function getMarketplace(): Marketplace {
        if (!$this->_marketplace) {
            $this->_marketplace = new Marketplace($this);
        }
        return $this->_marketplace;
    }

    /**
     * @return Wireless Version wireless of preview
     */
    protected function getWireless(): Wireless {
        if (!$this->_wireless) {
            $this->_wireless = new Wireless($this);
        }
        return $this->_wireless;
    }

    /**
     * Magic getter to lazy load version
     *
     * @param string $name Version to return
     * @return \Twilio\Version The requested version
     * @throws TwilioException For unknown versions
     */
    public function __get(string $name) {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown version ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return \Twilio\InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments) {
        $method = 'context' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return \call_user_func_array([$this, $method], $arguments);
        }

        throw new TwilioException('Unknown context ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Preview]';
    }
}
